import { Howl, HowlOptions } from "howler";
import { useEffect, useRef, useState } from "react";

interface UseAudioReturn {
  isPlaying: boolean;
  isLoaded: boolean;
  duration: number;
  seek: number;
  play: () => void;
  pause: () => void;
  toggle: () => void;
  seekTo: (time: number) => void;
}

interface UseAudioOptions {
  autoplay?: boolean;
  volume?: number;
  format?: string[];
}

export const useAudio = (
  audioSrc: string,
  options?: UseAudioOptions
): UseAudioReturn => {
  const {
    autoplay = false,
    volume = 1.0,
    format = ["mp3", "aac", "ogg"],
  } = options || {};

  const [isPlaying, setIsPlaying] = useState(false);
  const [isLoaded, setIsLoaded] = useState(false);
  const [duration, setDuration] = useState(0);
  const [seek, setSeek] = useState(0);
  const soundRef = useRef<Howl | null>(null);

  useEffect(() => {
    // only run on client and if audioSrc is defined
    if (typeof window === "undefined" || !audioSrc) return;

    // create Howl options
    const soundOptions: HowlOptions = {
      src: [audioSrc],
      html5: false,
      format,
      volume,
      onload: () => {
        setIsLoaded(true);
        setDuration(soundRef.current?.duration() || 0);
        if (autoplay) {
          soundRef.current?.play();
        }
      },
      onplay: () => setIsPlaying(true),
      onpause: () => setIsPlaying(false),
      onend: () => {
        setIsPlaying(false);
        setSeek(0);
      },
      onseek: () => {
        const currentSeek = soundRef.current?.seek() || 0;
        setSeek(currentSeek);
      },
      onstop: () => {
        setIsPlaying(false);
      },
    };

    const sound = new Howl(soundOptions);
    soundRef.current = sound;

    if (autoplay) {
      sound.play();
    }

    return () => {
      soundRef.current?.unload();
    };
  }, [audioSrc, autoplay, volume, format]);

  const play = () => {
    if (soundRef.current && !isPlaying) {
      soundRef.current.play();
    }
  };

  const pause = () => {
    if (soundRef.current && isPlaying) {
      soundRef.current.pause();
    }
  };

  const toggle = () => {
    if (isPlaying) {
      pause();
    } else {
      play();
    }
  };

  const seekTo = (seconds: number) => {
    if (soundRef.current) {
      soundRef.current.seek(seconds);
      setSeek(seconds);
    }
  };

  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (isPlaying) {
      interval = setInterval(() => {
        if (soundRef.current) {
          const currentSeek = soundRef.current.seek();
          if (currentSeek !== undefined) {
            setSeek(currentSeek);
          }
        }
      }, 1000);
    }

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [isPlaying]);

  return {
    isPlaying,
    isLoaded,
    duration,
    seek,
    play,
    pause,
    toggle,
    seekTo,
  };
};

export type { UseAudioReturn };
