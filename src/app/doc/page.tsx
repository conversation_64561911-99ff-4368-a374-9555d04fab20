import AudioPlayer from "@/components/AudioPlayer";

export default function Page() {
  return (
    <AudioPlayer
      audioUrl="http://localhost:9000/test/01%EF%BD%9C%E6%80%A7%E8%83%BD%E4%BC%98%E5%8C%96%E6%B5%81%E7%A8%8B%EF%BC%9A%E5%88%9A%E5%BC%80%E5%A7%8B%E5%81%9A%E6%80%A7%E8%83%BD%E4%BC%98%E5%8C%96%E4%BB%8E%E4%BD%95%E5%85%A5%E6%89%8B%EF%BC%9F.mp3?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=6PC9G9M3EA1ZV18N4B29%2F20250906%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250906T062941Z&X-Amz-Expires=3600&X-Amz-Security-Token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJwYXJlbnQiOiJydXN0ZnNhZG1pbiIsImV4cCI6MTc1NzE4MzI4OX0.P81qscbI45cG8mghkNzXyrdDFNxhclqN1jfQNbn2mhSsXnU2GW51JEKLwo8lNVVeHsIfTlVpSWQZWwov2gFIfw&X-Amz-Signature=a1e300f704e4ff58f92030b455ff47b14816939081f68fa3a1c7eb346294e08b&X-Amz-SignedHeaders=host&x-amz-checksum-mode=ENABLED&x-id=GetObject
    "
    />
  );
}
