"use client";

import { useAudio, UseAudioReturn } from "@/hooks/useAudio";
import { formatTime } from "@/lib/audioUtils";
import * as Slider from "@radix-ui/react-slider";
import { AnimatePresence, motion } from "framer-motion";
import { Pause, Play, RotateCcw } from "lucide-react";
import { Button } from "./ui/button";

interface AudioPlayerProps {
  audioUrl: string;
  title?: string;
  subtitle?: string;
  autoplay?: boolean;
  className?: string;
}

export default function AudioPlayer({
  audioUrl,
  title = "音频播放器",
  subtitle,
  autoplay = false,
  className = "",
}: AudioPlayerProps) {
  const {
    isPlaying,
    isLoaded,
    duration,
    seek,
    toggle,
    seekTo,
  }: UseAudioReturn = useAudio(audioUrl, { autoplay });

  const progress = duration > 0 ? (seek / duration) * 100 : 0;
  const handlePlayClick = () => {
    if (!isLoaded) return;
    if (seek >= duration && duration > 0) {
      seekTo(0);
      toggle();
    } else {
      toggle();
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className={`
        relative p-5 bg-white dark:bg-gray-900 rounded-xl 
        shadow-lg border border-gray-200 dark:border-gray-800
        backdrop-blur-sm bg-opacity-90 dark:bg-opacity-80
        max-w-md w-full mx-auto ${className}
      `}
    >
      {/* 标题区域 */}
      <div className="mb-4 space-y-1">
        <h3 className="text-sm font-semibold text-gray-800 dark:text-gray-100">
          {title}
        </h3>
        {subtitle && (
          <p className="text-xs text-gray-500 dark:text-gray-400">{subtitle}</p>
        )}
      </div>

      {/* 进度条 */}
      <div className="mb-4">
        <Slider.Root
          className="relative flex items-center w-full h-5"
          value={[seek]}
          max={duration || 100}
          step={1}
          onValueChange={(val) => seekTo(val[0])}
          disabled={!isLoaded}
        >
          <Slider.Track className="bg-gray-200 dark:bg-gray-700 relative rounded-full w-full h-2">
            <Slider.Range className="absolute bg-gradient-to-r from-blue-500 to-purple-500 rounded-full h-full" />
          </Slider.Track>
          <AnimatePresence>
            <motion.div
              key="thumb"
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              exit={{ scale: 0 }}
              transition={{ type: "spring", stiffness: 500, damping: 20 }}
            >
              <Slider.Thumb
                className={`
                  block w-4 h-4 bg-white dark:bg-gray-200 
                  shadow-md rounded-full 
                  focus:outline-none focus:ring-2 focus:ring-blue-400 dark:focus:ring-blue-500
                  border border-gray-300 dark:border-gray-600
                  cursor-grab active:cursor-grabbing
                  transition-transform hover:scale-110
                `}
                aria-label="进度调节"
              />
            </motion.div>
          </AnimatePresence>
        </Slider.Root>

        {/* 时间显示 */}
        <div className="flex justify-between mt-2 text-xs text-gray-500 dark:text-gray-400">
          <span>{formatTime(seek)}</span>
          <span>{formatTime(duration)}</span>
        </div>
      </div>

      {/* 控制按钮 */}
      <div className="flex items-center justify-between">
        <Button
          variant="ghost"
          size="icon"
          className="text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800"
          onClick={() => seekTo(0)}
          disabled={!isLoaded}
        >
          <RotateCcw className="h-4 w-4" />
        </Button>
        <motion.div whileTap={{ scale: 0.95 }} whileHover={{ scale: 1.05 }}>
          <Button
            onClick={handlePlayClick}
            disabled={!isLoaded}
            className={`
              group relative w-10 h-10 rounded-full 
              bg-gradient-to-r from-blue-500 to-purple-600 
              hover:from-blue-600 hover:to-purple-700
              text-white font-bold shadow-md
              flex items-center justify-center
              transition-all duration-200
              disabled:from-gray-300 disabled:to-gray-400 
              disabled:cursor-not-allowed
              focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-offset-2
            `}
            aria-label={isPlaying ? "暂停" : "播放"}
          >
            <AnimatePresence mode="wait">
              <motion.div
                key={isPlaying ? "pause" : "play"}
                initial={{ opacity: 0, rotate: isPlaying ? -15 : 15 }}
                animate={{ opacity: 1, rotate: 0 }}
                exit={{ opacity: 0, rotate: isPlaying ? 15 : -15 }}
                transition={{ duration: 0.2 }}
              >
                {isPlaying ? (
                  <Pause className="h-4 w-4" />
                ) : (
                  <Play className="h-4 w-4 ml-[1px]" />
                )}
              </motion.div>
            </AnimatePresence>
          </Button>
        </motion.div>
        <div className="w-10 h-10" /> {/* 占位符，保持居中 */}
      </div>

      {/* 加载状态提示 */}
      {!isLoaded && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="absolute inset-0 bg-white/70 dark:bg-gray-900/70 rounded-xl flex items-center justify-center"
        >
          <span className="text-xs text-gray-500 dark:text-gray-400">
            加载中...
          </span>
        </motion.div>
      )}
    </motion.div>
  );
}
