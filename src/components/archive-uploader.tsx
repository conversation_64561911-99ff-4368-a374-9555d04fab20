"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import {
  closestCenter,
  DndContext,
  DragEndEvent,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
} from "@dnd-kit/core";
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  useSortable,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import JSZip from "jszip";
import {
  Download,
  Edit2,
  File,
  Folder,
  RefreshCw,
  Send,
  Upload,
} from "lucide-react";
import { useCallback, useState } from "react";
import { useDropzone } from "react-dropzone";

interface FileNode {
  id: string;
  name: string;
  type: "file" | "folder";
  path: string;
  content?: Uint8Array;
  children?: FileNode[];
  parent?: string;
}

interface SortableItemProps {
  id: string;
  node: FileNode;
  onRename: (id: string, newName: string) => void;
  depth: number;
}

function SortableItem({ id, node, onRename, depth }: SortableItemProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [editName, setEditName] = useState(node.name);

  const { attributes, listeners, setNodeRef, transform, transition } =
    useSortable({ id });

  const handleRename = () => {
    onRename(id, editName);
    setIsEditing(false);
  };

  // Calculate depth based on path
  const calculatedDepth = node.path.split("/").length - 1;
  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    marginLeft: calculatedDepth * 20,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...attributes}
      {...listeners}
      className={`flex items-center gap-2 p-2 border rounded hover:bg-gray-50 cursor-move mb-1`}
    >
      {node.type === "folder" ? (
        <Folder className="w-4 h-4 text-blue-500" />
      ) : (
        <File className="w-4 h-4 text-gray-500" />
      )}

      {isEditing ? (
        <div className="flex items-center gap-2 flex-1">
          <Input
            value={editName}
            onChange={(e) => setEditName(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === "Enter") handleRename();
              if (e.key === "Escape") setIsEditing(false);
            }}
            className="flex-1"
            autoFocus
          />
          <Button size="sm" onClick={handleRename}>
            Save
          </Button>
        </div>
      ) : (
        <>
          <span className="flex-1 text-sm">{node.name}</span>
          <span className="text-xs text-gray-400 mr-2">{node.type}</span>
          <Button
            size="sm"
            variant="ghost"
            onClick={(e) => {
              e.stopPropagation();
              setIsEditing(true);
            }}
          >
            <Edit2 className="w-3 h-3" />
          </Button>
        </>
      )}
    </div>
  );
}

export default function ArchiveUploader() {
  const [files, setFiles] = useState<FileNode[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [jsonData, setJsonData] = useState<any>(null);
  const [uploadedFileName, setUploadedFileName] = useState<string>("");

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    const file = acceptedFiles[0];
    if (!file) return;

    setIsProcessing(true);
    setUploadedFileName(file.name);

    try {
      if (file.name.endsWith(".zip")) {
        await processZipFile(file);
      } else if (file.name.endsWith(".tar.gz") || file.name.endsWith(".tgz")) {
        // For now, we'll focus on ZIP files
        // TAR.GZ support can be added later with a library like tar-js
        alert("TAR.GZ support coming soon. Please use ZIP files for now.");
      }
    } catch (error) {
      console.error("Error processing file:", error);
      alert("Error processing file. Please try again.");
    } finally {
      setIsProcessing(false);
    }
  }, []);

  const processZipFile = async (file: File) => {
    const zip = new JSZip();
    const zipContent = await zip.loadAsync(file);
    const pathMap = new Map<string, FileNode>();
    const folderSet = new Set<string>();

    // First pass: collect all folders
    for (const [path, zipEntry] of Object.entries(zipContent.files)) {
      if (zipEntry.dir) {
        folderSet.add(path.replace(/\/$/, "")); // Remove trailing slash
      } else {
        // Add parent folders for files
        const pathParts = path.split("/");
        for (let i = 1; i < pathParts.length; i++) {
          const folderPath = pathParts.slice(0, i).join("/");
          if (folderPath) {
            folderSet.add(folderPath);
          }
        }
      }
    }

    // Create folder nodes
    for (const folderPath of folderSet) {
      const pathParts = folderPath.split("/");
      const folderName = pathParts[pathParts.length - 1];

      const node: FileNode = {
        id: folderPath,
        name: folderName,
        type: "folder",
        path: folderPath,
        children: [],
      };

      pathMap.set(folderPath, node);
    }

    // Create file nodes
    for (const [path, zipEntry] of Object.entries(zipContent.files)) {
      if (zipEntry.dir) continue;

      const content = await zipEntry.async("uint8array");
      const pathParts = path.split("/");
      const fileName = pathParts[pathParts.length - 1];

      const node: FileNode = {
        id: path,
        name: fileName,
        type: "file",
        path: path,
        content: content,
      };

      pathMap.set(path, node);
    }

    // Build tree structure and flatten for display
    const rootItems: FileNode[] = [];
    const allItems: FileNode[] = [];

    // Sort all paths to ensure proper hierarchy
    const sortedPaths = Array.from(pathMap.keys()).sort();

    for (const path of sortedPaths) {
      const node = pathMap.get(path)!;
      const pathParts = path.split("/");

      if (pathParts.length === 1) {
        // Root level item
        rootItems.push(node);
        allItems.push(node);
      } else {
        // Child item - add to flattened list for display
        allItems.push(node);
      }
    }

    // Sort items by name within each level
    allItems.sort((a, b) => {
      const aDepth = a.path.split("/").length;
      const bDepth = b.path.split("/").length;

      if (aDepth !== bDepth) {
        return aDepth - bDepth;
      }

      return a.name.localeCompare(b.name);
    });

    setFiles(allItems);
    updateJsonData(allItems);
  };

  const updateJsonData = (fileList: FileNode[]) => {
    const jsonStructure = fileList.map((file) => ({
      id: file.id,
      name: file.name,
      type: file.type,
      path: file.path,
      parent: file.parent || null,
    }));
    setJsonData(jsonStructure);
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (active.id !== over?.id) {
      setFiles((items) => {
        const oldIndex = items.findIndex((item) => item.id === active.id);
        const newIndex = items.findIndex((item) => item.id === over?.id);

        const newItems = arrayMove(items, oldIndex, newIndex);
        updateJsonData(newItems);
        return newItems;
      });
    }
  };

  const handleRename = (id: string, newName: string) => {
    setFiles((prev) => {
      const newFiles = prev.map((file) =>
        file.id === id ? { ...file, name: newName } : file
      );
      updateJsonData(newFiles);
      return newFiles;
    });
  };

  const createArchive = async () => {
    const zip = new JSZip();

    // Add JSON structure file
    zip.file("structure.json", JSON.stringify(jsonData, null, 2));

    // Add all files with their updated names and paths
    files.forEach((file) => {
      if (file.content && file.type === "file") {
        // Use the current name and path structure
        const pathParts = file.path.split("/");
        pathParts[pathParts.length - 1] = file.name; // Use updated name
        const newPath = pathParts.join("/");
        zip.file(newPath, file.content);
      }
    });

    return await zip.generateAsync({ type: "blob" });
  };

  const handleSubmit = async () => {
    try {
      setIsProcessing(true);
      const archiveBlob = await createArchive();

      const formData = new FormData();
      formData.append("file", archiveBlob, "processed-archive.zip");

      const response = await fetch(
        "http://localhost:8081/api/v1/knowsets/import",
        {
          method: "POST",
          body: formData,
        }
      );

      if (response.ok) {
        alert("Archive submitted successfully!");
      } else {
        throw new Error("Failed to submit archive");
      }
    } catch (error) {
      console.error("Error submitting archive:", error);
      alert("Error submitting archive. Please try again.");
    } finally {
      setIsProcessing(false);
    }
  };

  const handleDownload = async () => {
    try {
      setIsProcessing(true);
      const archiveBlob = await createArchive();

      const url = URL.createObjectURL(archiveBlob);
      const a = document.createElement("a");
      a.href = url;
      a.download = "processed-archive.zip";
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error("Error creating download:", error);
      alert("Error creating download. Please try again.");
    } finally {
      setIsProcessing(false);
    }
  };

  const handleReset = () => {
    setFiles([]);
    setJsonData(null);
    setUploadedFileName("");
  };

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      "application/zip": [".zip"],
      "application/gzip": [".tar.gz", ".tgz"],
    },
    multiple: false,
  });

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Archive File Processor</CardTitle>
            {files.length > 0 && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleReset}
                className="flex items-center gap-2"
              >
                <RefreshCw className="w-4 h-4" />
                Reset
              </Button>
            )}
          </div>
          {uploadedFileName && (
            <p className="text-sm text-muted-foreground">
              Uploaded: {uploadedFileName}
            </p>
          )}
        </CardHeader>
        <CardContent>
          <div
            {...getRootProps()}
            className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
              isDragActive
                ? "border-blue-500 bg-blue-50"
                : "border-gray-300 hover:border-gray-400"
            }`}
          >
            <input {...getInputProps()} />
            <Upload className="w-12 h-12 mx-auto mb-4 text-gray-400" />
            {isDragActive ? (
              <p>Drop the archive file here...</p>
            ) : (
              <div>
                <p className="text-lg mb-2">
                  Drag & drop an archive file here, or click to select
                </p>
                <p className="text-sm text-gray-500">
                  Supports .zip and .tar.gz files
                </p>
              </div>
            )}
          </div>

          {isProcessing && (
            <div className="mt-4 text-center">
              <p>Processing archive file...</p>
            </div>
          )}
        </CardContent>
      </Card>

      {files.length > 0 && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                File Tree
                <span className="text-sm font-normal text-muted-foreground">
                  {files.filter((f) => f.type === "file").length} files,{" "}
                  {files.filter((f) => f.type === "folder").length} folders
                </span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <DndContext
                sensors={sensors}
                collisionDetection={closestCenter}
                onDragEnd={handleDragEnd}
              >
                <SortableContext
                  items={files.map((f) => f.id)}
                  strategy={verticalListSortingStrategy}
                >
                  <div className="space-y-2">
                    {files.map((file) => (
                      <SortableItem
                        key={file.id}
                        id={file.id}
                        node={file}
                        onRename={handleRename}
                        depth={0}
                      />
                    ))}
                  </div>
                </SortableContext>
              </DndContext>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>JSON Structure</CardTitle>
            </CardHeader>
            <CardContent>
              <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto max-h-96">
                {JSON.stringify(jsonData, null, 2)}
              </pre>
            </CardContent>
          </Card>
        </div>
      )}

      {files.length > 0 && (
        <Card>
          <CardContent className="pt-6">
            <div className="flex gap-4">
              <Button
                className="flex items-center gap-2"
                onClick={handleSubmit}
                disabled={isProcessing}
              >
                <Send className="w-4 h-4" />
                {isProcessing ? "Processing..." : "Submit to API"}
              </Button>
              <Button
                variant="outline"
                className="flex items-center gap-2"
                onClick={handleDownload}
                disabled={isProcessing}
              >
                <Download className="w-4 h-4" />
                Download Archive
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
